'use strict';
const db = uniCloud.database();
const axios = require('axios');

// 创建预配置的axios实例
const botAxios = axios.create({
  timeout: 10000, // 10秒超时
  headers: {
    'Content-Type': 'application/json'
  }
});

// 应用配置常量
const appConfig = {
  // Bot API 相关配置
  botApi: {
    baseUrl: 'https://asf.uyizu.com:10010/Api/Bot/',
    password: 'Lilong721520.',
    timeout: 10000
  }
};

/**
 * Result工具类 - 统一处理成功和失败状态
 */
class Result {
  constructor(success, data, error) {
    this.success = success;
    this.data = data;
    this.error = error;
  }
  
  static ok(data) {
    return new Result(true, data, null);
  }
  
  static fail(error) {
    return new Result(false, null, error);
  }
  
  isSuccess() {
    return this.success;
  }
  
  isFail() {
    return !this.success;
  }
}

/**
 * 安全执行异步函数，捕获异常并返回Result
 * @param {Function} asyncFn 异步函数
 * @param {String} errorContext 错误上下文
 * @returns {Promise<Result>} Result对象
 */
async function safeExecute(asyncFn, errorContext = '操作失败') {
  try {
    const result = await asyncFn();
    return Result.ok(result);
  } catch (error) {
    console.error(`${errorContext}:`, error);
    return Result.fail(error.message || errorContext);
  }
}

/**
 * 统一异常处理装饰器
 * @param {Function} handler 业务处理函数
 * @returns {Function} 包装后的函数
 */
function withErrorHandling(handler) {
  return async (...args) => {
    try {
      return await handler(...args);
    } catch (error) {
      console.error('未捕获的异常:', error);
      return { code: -1, message: '处理请求失败' };
    }
  };
}

/**
 * 主函数入口
 */
exports.main = withErrorHandling(async (event, context) => {
  const { action, params = {} } = event;
  const { uniIdToken } = event;

  console.log('收到请求:', { action, params });

  if (!action) {
    return { code: -1, message: '缺少action参数' };
  }

  if (!uniIdToken) {
    return { code: -1, message: '用户未登录' };
  }

  if (action === 'getToken') {
    return await handleGetTokenByCharacterName(params, uniIdToken, context);
  }

  return { code: -1, message: '未知操作: ' + action };
});

/**
 * 处理通过角色名获取令牌请求
 * @param {Object} params 请求参数
 * @param {String} uniIdToken 用户token
 * @param {Object} context 云函数上下文
 */
async function handleGetTokenByCharacterName(params, uniIdToken, context) {
  const startTime = Date.now();
  const { characterName } = params;

  // 参数验证
  if (!characterName || typeof characterName !== 'string') {
    return { code: -1, message: '请输入有效的角色名' };
  }

  const trimmedCharacterName = characterName.trim();
  if (!trimmedCharacterName) {
    return { code: -1, message: '角色名不能为空' };
  }

  // 角色名格式验证
  if (!/^[A-Z]{2}\d+$/.test(trimmedCharacterName)) {
    return { code: -1, message: '角色名格式错误，应为UE0001格式' };
  }

  // 获取用户信息
  const userInfoResult = await getUserInfo(uniIdToken);
  if (userInfoResult.isFail()) {
    return { code: -1, message: userInfoResult.error };
  }

  const userInfo = userInfoResult.data;
  console.log('用户信息获取成功，用户ID:', userInfo._id);

  // 验证用户是否已实名认证
  if (!isUserCertified(userInfo)) {
    return { code: -1, message: '请先完成实名认证' };
  }

  // 调用Bot API获取令牌
  const tokenResult = await getBotToken(trimmedCharacterName);
  const responseTime = Date.now() - startTime;

  // 构建日志数据
  const logData = {
    user_id: userInfo._id,
    character_name: trimmedCharacterName,
    query_result: tokenResult.isSuccess() ? 'success' : 'failed',
    response_time: responseTime,
    ip_address: context.CLIENTIP || 'unknown',
    user_agent: context.HEADERS?.['user-agent'] || 'unknown'
  };

  if (tokenResult.isSuccess()) {
    logData.token_value = tokenResult.data;
    console.log(`用户 ${userInfo._id} 获取令牌成功，角色名: ${trimmedCharacterName}`);
  } else {
    logData.error_message = tokenResult.error;
    console.log(`用户 ${userInfo._id} 获取令牌失败，角色名: ${trimmedCharacterName}，错误: ${tokenResult.error}`);
  }

  // 异步记录日志，不阻塞主流程
  recordQueryLog(logData);

  if (tokenResult.isFail()) {
    return { code: -1, message: tokenResult.error };
  }

  return {
    code: 0,
    message: '获取令牌成功',
    steamToken: tokenResult.data
  };
}



/**
 * 验证用户是否已完成实名认证
 * @param {Object} userInfo 用户信息
 * @returns {Boolean} 是否已认证
 */
function isUserCertified(userInfo) {
  return userInfo?.realname_auth?.auth_status === 2;
}

/**
 * 调用Bot API获取令牌
 * @param {String} characterName 角色名
 * @returns {Promise<Result>} 包含令牌的Result对象
 */
async function getBotToken(characterName) {
  return await safeExecute(async () => {
    const url = `${appConfig.botApi.baseUrl}${encodeURIComponent(characterName)}/TwoFactorAuthentication/Token`;
    console.log('请求Bot API URL:', url);

    try {
      const response = await botAxios.get(url, {
        params: {
          password: appConfig.botApi.password
        },
        timeout: appConfig.botApi.timeout
      });

      console.log('Bot API响应:', JSON.stringify(response.data));
      const { data } = response;

      // 处理失败响应：{"Result":null,"Message":"找不到任何名为 UE00012 的机器人！","Success":false}
      if (!data || !data.Success) {
        throw new Error(data?.Message || '服务器返回失败状态');
      }

      // 处理成功响应但Result为null的情况
      if (!data.Result) {
        throw new Error('未找到对应的机器人');
      }

      // 获取角色对应的结果
      const characterResult = data.Result[characterName];
      if (!characterResult) {
        throw new Error(`未找到角色 ${characterName} 的数据`);
      }

      // 检查角色结果是否成功
      if (!characterResult.Success) {
        throw new Error(characterResult.Message || '获取令牌失败');
      }

      // 返回令牌值
      return characterResult.Result || '';

    } catch (axiosError) {
      // 处理HTTP错误
      if (axiosError.response) {
        const status = axiosError.response.status;
        const statusText = axiosError.response.statusText;

        if (status === 400) {
          throw new Error('请求参数错误，请检查角色名格式');
        } else if (status === 401) {
          throw new Error('API认证失败');
        } else if (status === 404) {
          throw new Error('API接口不存在');
        } else if (status >= 500) {
          throw new Error('服务器内部错误，请稍后重试');
        } else {
          throw new Error(`API请求失败: ${status} ${statusText}`);
        }
      } else if (axiosError.request) {
        throw new Error('网络连接失败，请检查网络状态');
      } else {
        throw axiosError;
      }
    }
  }, '调用Bot API失败');
}

/**
 * 验证token并获取用户信息
 * @param {String} uniIdToken 用户token
 * @returns {Promise<Result>} 包含用户信息的Result对象
 */
async function getUserInfo(uniIdToken) {
  return await safeExecute(async () => {
    const { result } = await uniCloud.callFunction({
      name: 'user-auth',
      data: {
        action: 'getUserInfo',
        uniIdToken
      }
    });

    if (result.code !== 0) {
      throw new Error('未登录或登录已过期');
    }

    return result.userInfo;
  }, '获取用户信息失败');
}

/**
 * 记录查询日志
 * @param {Object} logData 日志数据
 * @returns {Promise<void>} 无返回值，错误会被记录但不影响主流程
 */
async function recordQueryLog(logData) {
  // 参数验证
  if (!logData || !logData.user_id || !logData.character_name) {
    console.error('日志数据不完整，跳过记录');
    return;
  }

  const result = await safeExecute(async () => {
    const now = new Date();
    // 生成北京时间字符串 (UTC+8)
    const beijingTime = new Date(now.getTime() + 8 * 60 * 60 * 1000);
    const timeStr = beijingTime.toISOString().replace('T', ' ').replace('Z', '').substring(0, 23);

    const logRecord = {
      ...logData,
      query_time: timeStr,
      // 确保必要字段存在
      response_time: logData.response_time || 0,
      ip_address: logData.ip_address || 'unknown',
      user_agent: logData.user_agent || 'unknown'
    };

    await db.collection('token-query-logs').add(logRecord);
    console.log('查询日志记录成功:', logData.user_id, logData.character_name, logData.query_result);
  }, '记录查询日志失败');

  if (result.isFail()) {
    console.error('记录查询日志失败:', result.error, '数据:', logData);
  }
}
