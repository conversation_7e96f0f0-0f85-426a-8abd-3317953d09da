{"bsonType": "object", "required": ["user_id", "query_time", "character_name", "query_result"], "properties": {"_id": {"description": "ID，系统自动生成"}, "user_id": {"bsonType": "string", "description": "用户ID，关联uni-id-users表"}, "query_time": {"bsonType": "string", "description": "查询时间，格式：yyyy-MM-dd HH:mm:ss SSS（北京时间）"}, "character_name": {"bsonType": "string", "description": "查询的角色名"}, "query_result": {"bsonType": "string", "description": "查询结果状态：success-成功，failed-失败，rate_limited-频率限制"}, "token_value": {"bsonType": "string", "description": "返回的令牌值（成功时记录）"}, "ip_address": {"bsonType": "string", "description": "请求来源IP地址"}, "error_message": {"bsonType": "string", "description": "错误信息（失败时记录）"}, "response_time": {"bsonType": "int", "description": "API响应时间（毫秒）"}, "user_agent": {"bsonType": "string", "description": "用户代理信息"}}, "permission": {"read": "doc.user_id == auth.uid || 'READ_TOKEN_QUERY_LOGS' in auth.permission", "create": "auth.uid != null", "update": false, "delete": "'DELETE_TOKEN_QUERY_LOGS' in auth.permission"}}